import type { Metada<PERSON> } from 'next'
import { Inter, JetBrains_Mono } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/components/providers/AuthProvider'
import { ToastProvider } from '@/components/providers/ToastProvider'
import { ThemeProvider } from '@/components/providers/ThemeProvider'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
})

export const metadata: Metadata = {
  title: 'CyberGuard Pro - Advanced Cybersecurity Training Platform',
  description: 'Master cybersecurity with interactive training modules, realistic phishing simulations, and comprehensive threat awareness programs. Protect your organization with cutting-edge security education.',
  keywords: ['cybersecurity', 'phishing simulation', 'security training', 'threat awareness', 'cyber defense', 'security education', 'penetration testing', 'social engineering'],
  authors: [{ name: '<PERSON>' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
  openGraph: {
    title: 'CyberGuard Pro - Advanced Cybersecurity Training',
    description: 'Interactive cybersecurity training and phishing simulation platform',
    type: 'website',
    locale: 'en_US',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange={false}
        >
          <AuthProvider>
            <ToastProvider>
              <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
                {children}
              </div>
            </ToastProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
